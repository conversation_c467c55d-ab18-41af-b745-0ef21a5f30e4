# 字段映射智能匹配算法优化测试

## 优化内容

### 1. 数据透视配置优先级匹配
- **最高优先级**：利用数据透视配置中已选择的字段
- **行维度字段**：优先匹配到图表的维度字段
- **列维度字段**：优先匹配到图表的分类字段
- **指标字段**：优先匹配到图表的数值字段
- **筛选字段**：可用于各种字段类型

### 2. 增强的匹配算法
- **数据类型兼容性检查**：确保字段类型匹配
- **字段角色推断**：根据字段名称推断其在图表中的作用
- **语义关键词匹配**：支持中英文关键词匹配
- **评分机制**：多维度评分，选择最佳匹配

### 3. 评分规则

#### 优先级得分（最高优先级）
- 数据透视指标字段：+100分
- 数据透视行维度字段：+90分
- 数据透视列维度字段：+85分
- 数据透视时间维度字段：+80分

#### 数据类型匹配
- 类型匹配：+50分
- 类型不匹配：-20分

#### 名称匹配
- 完全匹配：+40分
- 包含匹配：+30分

#### 字段角色匹配
- 数值类型适合指标：+25分
- 日期类型适合时间轴：+25分
- 字符串类型适合分类维度：+20分

#### 关键词匹配
- 时间关键词匹配：+15分
- 指标关键词匹配：+15分
- 分类关键词匹配：+10分

## 测试场景

### 场景1：基础匹配测试
1. 创建数据集，包含字段：
   - `国家` (String)
   - `年份` (Date)
   - `GDP` (Number)
   - `人口` (Number)

2. 选择柱状图模板，包含字段映射：
   - `XField` (X轴字段)
   - `YField` (Y轴字段)
   - `SeriesField` (系列字段)

3. 不配置数据透视，直接智能匹配
   - 预期：基于字段名称和类型进行基础匹配

### 场景2：数据透视优化匹配测试
1. 使用相同的数据集和模板

2. 先配置数据透视：
   - 行维度：`国家`
   - 列维度：`年份`
   - 指标：`GDP` (求和)

3. 再进行智能匹配
   - 预期：`XField` 匹配到 `年份`（列维度）
   - 预期：`YField` 匹配到 `GDP`（指标）
   - 预期：`SeriesField` 匹配到 `国家`（行维度）

### 场景3：复杂字段匹配测试
1. 创建包含复杂字段的模板（如对象数组）

2. 配置数据透视后进行匹配
   - 验证子字段的智能匹配

## 使用说明

### 最佳实践
1. **先配置数据透视**：在进行字段映射之前，先配置数据透视可以显著提高匹配准确性
2. **使用智能推荐**：点击数据透视的"智能推荐"按钮，系统会自动配置数据透视并触发字段映射
3. **查看匹配提示**：字段映射区域有提示图标，说明如何提高匹配准确性

### 操作流程
1. 选择图表模板和数据集
2. 点击数据透视的"智能推荐"按钮（推荐）
3. 或手动配置数据透视
4. 点击字段映射的"智能匹配"按钮
5. 检查匹配结果，手动调整不准确的映射

## 技术实现

### 核心函数
- `suggestFieldMapping()`: 主要的智能匹配函数
- `buildPivotFieldUsage()`: 构建数据透视字段使用情况
- `findBestMatchWithPivot()`: 增强的字段匹配算法
- `inferFieldRole()`: 字段角色推断
- `getCandidateFields()`: 获取候选字段并评分
- `isDataTypeCompatible()`: 数据类型兼容性检查
- `getFieldRoleScore()`: 字段角色匹配得分
- `getKeywordMatchScore()`: 关键词匹配得分

### 优化效果
- **提高匹配准确性**：通过数据透视配置提供上下文信息
- **减少手动配置**：自动化程度更高
- **更好的用户体验**：智能推荐 + 智能匹配的组合使用
- **支持复杂场景**：处理复杂字段和多维度数据
