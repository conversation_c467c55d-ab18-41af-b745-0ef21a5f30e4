<template>
  <div class="h-full flex flex-col">
    <div class="mt4 h-0 flex flex-1 flex-col bg-bg-container p4">
      <c-pro-table
        ref="tableRef"
        :api="getChartList"
        :columns="columns"
        :serial-number="true"
        row-key="id"
        immediate
        bordered
      >
        <template #header>
          <a-button type="primary" @click="openEditModal()">新增图表</a-button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'chartType'">
            {{ record.chartType?.name || '未设置' }}
          </template>
          <template v-else-if="column.key === 'dataset'">
            {{ record.dataset?.name || '未设置' }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a @click="openEditModal(record)">编辑</a>
            <a-divider type="vertical" />
            <a @click="previewChart(record)">预览</a>
            <a-divider type="vertical" />
            <a-popconfirm title="确定删除该图表？" @confirm="deleteChart(record)">
              <a>删除</a>
            </a-popconfirm>
          </template>
        </template>
      </c-pro-table>
    </div>

    <!-- 新增/编辑弹窗 - 优化为左右布局 -->
    <c-modal
      v-model:open="editVisible"
      title="图表编辑器"
      :style="{ top: '20px' }"
      :loading="saveLoading"
      :full-modal="true"
      @ok="handleEditOk"
      @cancel="editVisible = false"
    >
      <div class="h-full flex gap-4 border border-gray-200 rounded-lg bg-gray-50 p-4">
        <!-- 左侧操作栏 -->
        <div class="w-40% flex flex-col border-r-2 border-gray-300 rounded-l-lg bg-white pr-4 shadow-sm">
          <!-- 配置面板容器 -->
          <div class="flex flex-1 flex-col overflow-auto p-3 space-y-3">
            <div class="flex flex-1 flex-col space-y-3">
              <!-- 数据透视卡片 -->
              <div v-if="selectedDataset" class="config-card" :style="{ height: cardCollapsed.pivotConfig ? '40px' : `${panelHeights.pivotConfig}px` }">
                <div class="config-card-header" @mousedown="startResize('pivotConfig', $event)">
                  <div class="flex items-center gap-2">
                    <i class="i-ant-design:table-outlined text-orange-500" />
                    <span class="font-medium">数据透视</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <a-button v-if="!cardCollapsed.pivotConfig" size="small" @click="clearPivotConfig">
                      <div class="i-ant-design:clear-outlined" />
                    </a-button>
                    <a-button v-if="!cardCollapsed.pivotConfig" size="small" @click="suggestPivotConfig">
                      <div class="i-ant-design:bulb-outlined" />
                    </a-button>
                    <div
                      class="collapse-handle"
                      @click.stop="toggleCardCollapse('pivotConfig')"
                    >
                      <div
                        class="i-ant-design:up-outlined transition-transform duration-200"
                        :class="{ 'rotate-180': cardCollapsed.pivotConfig }"
                      />
                    </div>
                    <div class="resize-handle">
                      <i class="i-ant-design:drag-outlined" />
                    </div>
                  </div>
                </div>
                <div v-show="!cardCollapsed.pivotConfig" class="config-card-content">
                  <!-- 行维度配置 -->
                  <div class="mb-4">
                    <div class="config-label">行维度</div>
                    <a-select
                      v-model:value="pivotConfig.rowDimensions"
                      mode="multiple"
                      size="small"
                      style="width: 100%"
                      placeholder="请选择行维度字段"
                      allow-clear
                      @change="onConfigChange"
                    >
                      <a-select-option
                        v-for="field in availableFields"
                        :key="field.name"
                        :value="field.name"
                      >
                        {{ field.name }}
                      </a-select-option>
                    </a-select>
                  </div>

                  <!-- 列维度配置 -->
                  <div class="mb-4">
                    <div class="config-label">列维度</div>
                    <a-select
                      v-model:value="pivotConfig.columnDimensions"
                      mode="multiple"
                      size="small"
                      style="width: 100%"
                      placeholder="请选择列维度字段"
                      allow-clear
                      @change="onConfigChange"
                    >
                      <a-select-option
                        v-for="field in availableFields"
                        :key="field.name"
                        :value="field.name"
                      >
                        {{ field.name }}
                      </a-select-option>
                    </a-select>
                  </div>

                  <!-- 指标配置 -->
                  <div class="mb-4">
                    <div class="mb-2 flex items-center justify-between">
                      <span class="config-label">指标配置</span>
                      <a-button size="small" @click="addMetric">
                        <div class="i-ant-design:plus-outlined" />
                      </a-button>
                    </div>
                    <div v-if="pivotConfig.metrics && pivotConfig.metrics.length > 0" class="space-y-2">
                      <div
                        v-for="(metric, index) in pivotConfig.metrics"
                        :key="index"
                        class="flex items-center gap-2 border-2 border-green-200 rounded-lg bg-green-50 p-3"
                      >
                        <a-select
                          v-model:value="metric.valueField"
                          size="small"
                          style="flex: 1"
                          placeholder="选择字段"
                          @change="onConfigChange"
                        >
                          <a-select-option
                            v-for="field in numericFields"
                            :key="field.name"
                            :value="field.name"
                          >
                            {{ field.name }}
                          </a-select-option>
                        </a-select>
                        <a-select
                          v-model:value="metric.aggregation"
                          size="small"
                          style="width: 80px"
                          @change="onConfigChange"
                        >
                          <a-select-option :value="AggregationType.None">无聚合</a-select-option>
                          <a-select-option :value="AggregationType.Sum">求和</a-select-option>
                          <a-select-option :value="AggregationType.Count">计数</a-select-option>
                          <a-select-option :value="AggregationType.Avg">平均</a-select-option>
                          <a-select-option :value="AggregationType.Max">最大</a-select-option>
                          <a-select-option :value="AggregationType.Min">最小</a-select-option>
                        </a-select>
                        <a-button size="small" danger @click="removeMetric(index)">
                          <div class="i-ant-design:delete-outlined" />
                        </a-button>
                      </div>
                    </div>
                    <div v-else class="border border-gray-300 rounded border-dashed py-4 text-center text-xs text-gray-500">
                      点击添加指标
                    </div>
                  </div>

                  <!-- 筛选条件配置 -->
                  <div class="mb-4">
                    <div class="mb-2 flex items-center justify-between">
                      <span class="config-label">筛选条件</span>
                      <a-button size="small" @click="addFilter">
                        <div class="i-ant-design:plus-outlined" />
                      </a-button>
                    </div>
                    <div v-if="pivotConfig.filters && pivotConfig.filters.length > 0" class="space-y-2">
                      <div
                        v-for="(filter, index) in pivotConfig.filters"
                        :key="index"
                        class="border-2 border-orange-200 rounded-lg bg-orange-50 p-3"
                      >
                        <div class="mb-2">
                          <a-select
                            v-model:value="filter.field"
                            size="small"
                            style="width: 100%"
                            placeholder="选择字段"
                            @change="onConfigChange"
                          >
                            <a-select-option
                              v-for="field in availableFields"
                              :key="field.name"
                              :value="field.name"
                            >
                              {{ field.name }}
                            </a-select-option>
                          </a-select>
                        </div>
                        <div class="flex items-center gap-2">
                          <a-select
                            v-model:value="filter.operator"
                            size="small"
                            style="width: 100px"
                            @change="onConfigChange"
                          >
                            <a-select-option :value="FilterOperator.Equal">等于</a-select-option>
                            <a-select-option :value="FilterOperator.NotEqual">不等于</a-select-option>
                            <a-select-option :value="FilterOperator.GreaterThan">大于</a-select-option>
                            <a-select-option :value="FilterOperator.GreaterEqual">大于等于</a-select-option>
                            <a-select-option :value="FilterOperator.LessThan">小于</a-select-option>
                            <a-select-option :value="FilterOperator.LessEqual">小于等于</a-select-option>
                            <a-select-option :value="FilterOperator.In">包含于</a-select-option>
                            <a-select-option :value="FilterOperator.NotIn">不包含于</a-select-option>
                            <a-select-option :value="FilterOperator.Like">模糊匹配</a-select-option>
                            <a-select-option :value="FilterOperator.NotLike">不匹配</a-select-option>
                          </a-select>
                          <a-input
                            v-model:value="filter.value"
                            size="small"
                            style="flex: 1"
                            placeholder="筛选值"
                            @input="onConfigChange"
                          />
                          <a-button size="small" danger @click="removeFilter(index)">
                            <div class="i-ant-design:delete-outlined" />
                          </a-button>
                        </div>
                      </div>
                    </div>
                    <div v-else class="border border-gray-300 rounded border-dashed py-4 text-center text-xs text-gray-500">
                      点击添加筛选条件
                    </div>
                  </div>
                </div>
              </div>

              <!-- 字段映射卡片 -->
              <div class="config-card" :style="{ height: cardCollapsed.fieldMapping ? '40px' : `${panelHeights.fieldMapping}px` }">
                <div class="config-card-header" @mousedown="startResize('fieldMapping', $event)">
                  <div class="flex items-center gap-2">
                    <i class="i-ant-design:node-index-outlined text-green-500" />
                    <span class="font-medium">字段映射</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <a-button v-if="selectedTemplate && selectedDataset && !cardCollapsed.fieldMapping" size="small" @click="suggestFieldMapping">
                      <template #icon>
                        <i class="i-ant-design:bulb-outlined" />
                      </template>
                      智能匹配
                    </a-button>
                    <a-tooltip v-if="selectedTemplate && selectedDataset && !cardCollapsed.fieldMapping" title="先配置数据透视可以提高字段匹配的准确性">
                      <i class="i-ant-design:info-circle-outlined cursor-help text-blue-500" />
                    </a-tooltip>
                    <div
                      class="collapse-handle"
                      @click.stop="toggleCardCollapse('fieldMapping')"
                    >
                      <i
                        class="i-ant-design:up-outlined transition-transform duration-200"
                        :class="{ 'rotate-180': cardCollapsed.fieldMapping }"
                      />
                    </div>
                    <div class="resize-handle">
                      <i class="i-ant-design:drag-outlined" />
                    </div>
                  </div>
                </div>
                <div v-show="!cardCollapsed.fieldMapping" class="config-card-content">
                  <div v-if="selectedTemplate && selectedDataset">
                    <div v-for="(config, index) in fieldConfigs" :key="index" class="mb-3 border-2 border-gray-200 rounded-lg bg-gray-50 p-3 transition-colors hover:border-blue-300">
                      <div class="mb-2 flex items-center">
                        <span class="mr-2 text-sm text-gray-700 font-medium">{{ getFieldMappingDisplayName(config.fieldMapName) }}</span>
                        <a-tag size="small" :color="getFieldMappingDataType(config.fieldMapName) === FieldDataType.ObjectArray ? 'blue' : 'green'">
                          {{ getFieldMappingDataTypeText(config.fieldMapName) }}
                        </a-tag>
                      </div>

                      <!-- 简单字段映射 -->
                      <template v-if="!isComplexField(config.fieldMapName)">
                        <a-select
                          v-model:value="config.dataFieldName"
                          size="small"
                          style="width: 100%"
                          placeholder="请选择字段"
                          allow-clear
                          @change="onConfigChange"
                        >
                          <a-select-option
                            v-for="field in getAvailableFields(config.fieldMapName)"
                            :key="field.name"
                            :value="field.name"
                          >
                            {{ field.name }}
                          </a-select-option>
                        </a-select>
                      </template>

                      <!-- 复杂字段映射（对象数组） -->
                      <template v-else>
                        <div class="border border-blue-200 rounded-lg bg-blue-50 p-3">
                          <div class="mb-2 text-xs text-gray-600">
                            配置子字段映射：
                          </div>
                          <div v-for="(childMapping, childIndex) in getChildMappings(config.fieldMapName)" :key="childIndex" class="mb-2">
                            <div class="mb-1 text-xs text-gray-600">
                              {{ childMapping.displayName }}:
                            </div>
                            <a-select
                              :value="getChildFieldValue(config, childMapping.name)"
                              size="small"
                              style="width: 100%"
                              placeholder="请选择字段"
                              allow-clear
                              @change="(value) => { setChildFieldValue(config, childMapping.name, value); onConfigChange(); }"
                            >
                              <a-select-option
                                v-for="field in getAvailableFields(childMapping.name)"
                                :key="field.name"
                                :value="field.name"
                              >
                                {{ field.name }}
                              </a-select-option>
                            </a-select>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                  <div v-else class="py-8 text-center text-sm text-gray-500">
                    请先选择图表模板和数据集
                  </div>
                </div>
              </div>

              <!-- 样式配置卡片 -->
              <div v-if="selectedTemplate?.fieldMappings?.some(v => v.configType === ConfigType.StyleConfig)" class="config-card" :style="{ height: cardCollapsed.styleConfig ? '40px' : `${panelHeights.styleConfig}px` }">
                <div class="config-card-header" @mousedown="startResize('styleConfig', $event)">
                  <div class="flex items-center gap-2">
                    <i class="i-ant-design:bg-colors-outlined text-purple-500" />
                    <span class="font-medium">样式配置</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div
                      class="collapse-handle"
                      @click.stop="toggleCardCollapse('styleConfig')"
                    >
                      <i
                        class="i-ant-design:up-outlined transition-transform duration-200"
                        :class="{ 'rotate-180': cardCollapsed.styleConfig }"
                      />
                    </div>
                    <div class="resize-handle">
                      <i class="i-ant-design:drag-outlined" />
                    </div>
                  </div>
                </div>
                <div v-show="!cardCollapsed.styleConfig" class="config-card-content">
                  <div v-if="styleConfigs.length > 0">
                    <div v-for="(config, index) in styleConfigs" :key="index" class="mb-3">
                      <div class="config-label">
                        {{ getFieldMappingDisplayName(config.fieldMapName) }}
                      </div>
                      <a-input
                        v-model:value="config.dataFieldName"
                        size="small"
                        placeholder="请输入配置值"
                        @input="onConfigChange"
                      />
                    </div>
                  </div>
                  <div v-else class="py-8 text-center text-sm text-gray-500">
                    暂无样式配置项
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="flex flex-1 flex-col border-l border-gray-200 rounded-r-lg bg-white pl-4 shadow-sm">
          <!-- 基础信息编辑区域 -->
          <div class="mb-4 border-2 border-blue-100 rounded-lg bg-blue-50 p-4 shadow-sm">
            <div class="mb-3 flex items-center gap-2">
              <i class="i-ant-design:info-circle-outlined text-blue-500" />
              <span class="text-gray-800 font-medium">基础信息</span>
            </div>
            <div class="grid grid-cols-3 gap-4">
              <div class="form-group">
                <div class="config-label mb-1 text-xs text-gray-600">图表标题</div>
                <a-input
                  v-model:value="editForm.title"
                  size="small"
                  placeholder="请输入图表标题"
                  @input="onConfigChange"
                />
              </div>
              <div class="form-group">
                <div class="config-label mb-1 text-xs text-gray-600">图表模板</div>
                <a-select
                  v-model:value="editForm.chartTypeId"
                  size="small"
                  style="width: 100%"
                  placeholder="请选择图表模板"
                  @change="onChartTypeChange"
                >
                  <a-select-option
                    v-for="template in chartTemplates"
                    :key="template.id"
                    :value="template.id"
                  >
                    {{ template.name }}
                  </a-select-option>
                </a-select>
              </div>
              <div class="form-group">
                <div class="config-label mb-1 text-xs text-gray-600">数据集</div>
                <a-select
                  v-model:value="editForm.chartDatasetId"
                  size="small"
                  style="width: 100%"
                  placeholder="请选择数据集"
                  @change="onDatasetChange"
                >
                  <a-select-option
                    v-for="dataset in datasets"
                    :key="dataset.id"
                    :value="dataset.id"
                  >
                    {{ dataset.name }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </div>

          <!-- 预览控制栏 -->
          <div class="mb-3 flex items-center justify-between border-b border-gray-200 pb-3">
            <div class="text-base text-gray-800 font-medium">实时预览</div>
            <div class="flex items-center gap-2">
              <a-switch
                v-model:checked="autoRefresh"
                size="small"
                @change="onAutoRefreshChange"
              />
              <span class="text-sm text-gray-600">自动刷新</span>
              <a-button size="small" :disabled="!selectedDataset" @click="viewRawData">
                <template #icon>
                  <i class="i-ant-design:table-outlined" />
                </template>
                查看原始数据
              </a-button>
              <a-button size="small" :disabled="!currentRenderData" @click="viewRenderData">
                <template #icon>
                  <i class="i-ant-design:code-outlined" />
                </template>
                查看转换数据
              </a-button>
              <a-button size="small" :disabled="!selectedDataset || !currentRenderData" @click="viewDataCompare">
                <template #icon>
                  <i class="i-ant-design:diff-outlined" />
                </template>
                数据对比
              </a-button>
              <a-button size="small" :loading="previewLoading" @click="refreshPreview">
                <template #icon>
                  <i class="i-ant-design:reload-outlined" />
                </template>
                刷新
              </a-button>
            </div>
          </div>

          <div class="preview-container flex-1 border-2 border-gray-300 rounded-lg bg-white shadow-inner">
            <ChartRenderer :config="currentChartConfig" />
          </div>

          <div class="mt-2 text-center text-xs text-gray-500">
            {{ previewStatus }}
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 原始数据查看弹窗 -->
    <c-modal
      v-model:open="rawDataVisible"
      title="原始数据查看"
      :width="1200"
      :footer="null"
      :style="{ top: '20px' }"
    >
      <div class="h-600px flex flex-col">
        <!-- 数据信息栏 -->
        <div class="mb-4 flex items-center justify-between border-b border-gray-200 pb-3">
          <div class="flex items-center gap-4">
            <div class="text-base font-medium">
              数据集：{{ selectedDataset?.name || '未选择' }}
            </div>
            <div class="text-sm text-gray-600">
              共 {{ rawDataInfo.totalRows }} 行，{{ rawDataInfo.totalColumns }} 列
            </div>
          </div>
          <div class="flex items-center gap-2">
            <a-button size="small" :loading="rawDataLoading" @click="refreshRawData">
              <template #icon>
                <i class="i-ant-design:reload-outlined" />
              </template>
              刷新数据
            </a-button>
            <a-button size="small" :disabled="!rawData.length" @click="exportRawData">
              <template #icon>
                <i class="i-ant-design:download-outlined" />
              </template>
              导出数据
            </a-button>
          </div>
        </div>

        <!-- 数据表格 -->
        <div class="flex-1 overflow-auto border border-gray-200 rounded">
          <a-table
            :columns="rawDataColumns"
            :data-source="rawData"
            :pagination="{
              current: rawDataPagination.current,
              pageSize: rawDataPagination.pageSize,
              total: rawDataInfo.totalRows,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              pageSizeOptions: ['50', '100', '200', '500'],
            }"
            :loading="rawDataLoading"
            size="small"
            :scroll="{ x: 'max-content', y: 400 }"
            @change="handleRawDataTableChange"
          />
        </div>
      </div>
    </c-modal>

    <!-- 转换后数据查看弹窗 -->
    <c-modal
      v-model:open="renderDataVisible"
      title="转换后数据查看"
      :width="1200"
      :footer="null"
      :style="{ top: '20px' }"
    >
      <div class="h-600px flex flex-col">
        <!-- 数据信息栏 -->
        <div class="mb-4 flex items-center justify-between border-b border-gray-200 pb-3">
          <div class="flex items-center gap-4">
            <div class="text-base font-medium">
              转换后数据结构
            </div>
            <div class="text-sm text-gray-600">
              数据类型：{{ typeof currentRenderData }}
            </div>
          </div>
          <div class="flex items-center gap-2">
            <a-radio-group v-model:value="renderDataDisplayMode" size="small">
              <a-radio-button value="json">JSON视图</a-radio-button>
              <a-radio-button value="table" :disabled="!isRenderDataTableFormat">表格视图</a-radio-button>
            </a-radio-group>
            <a-button size="small" :disabled="!currentRenderData" @click="exportRenderData">
              <template #icon>
                <i class="i-ant-design:download-outlined" />
              </template>
              导出数据
            </a-button>
          </div>
        </div>

        <!-- 数据显示区域 -->
        <div class="flex-1 overflow-auto border border-gray-200 rounded">
          <!-- JSON 视图 -->
          <div v-if="renderDataDisplayMode === 'json'" class="h-full">
            <pre class="h-full overflow-auto whitespace-pre-wrap bg-gray-50 p-4 text-sm">{{ formatRenderDataJson }}</pre>
          </div>

          <!-- 表格视图 -->
          <div v-else-if="renderDataDisplayMode === 'table' && isRenderDataTableFormat" class="h-full">
            <a-table
              :columns="renderDataTableColumns"
              :data-source="renderDataTableData"
              :pagination="false"
              size="small"
              :scroll="{ x: 'max-content', y: 500 }"
            />
          </div>

          <!-- 不支持表格视图的提示 -->
          <div v-else class="h-full flex items-center justify-center text-gray-400">
            <div class="text-center">
              <i class="i-ant-design:info-circle-outlined mb-2 text-2xl" />
              <div>当前数据结构不支持表格视图</div>
              <div class="text-sm">请使用JSON视图查看数据</div>
            </div>
          </div>
        </div>
      </div>
    </c-modal>

    <!-- 数据对比查看弹窗 -->
    <c-modal
      v-model:open="dataCompareVisible"
      title="数据对比查看"
      :width="1400"
      :footer="null"
      :style="{ top: '20px' }"
    >
      <div class="h-700px flex flex-col">
        <!-- 对比控制栏 -->
        <div class="mb-4 flex items-center justify-between border-b border-gray-200 pb-3">
          <div class="text-base font-medium">
            数据对比：原始数据 vs 转换后数据
          </div>
          <div class="flex items-center gap-2">
            <a-radio-group v-model:value="compareActiveTab" size="small">
              <a-radio-button value="raw">原始数据</a-radio-button>
              <a-radio-button value="render">转换后数据</a-radio-button>
            </a-radio-group>
          </div>
        </div>

        <!-- 对比内容区域 -->
        <div class="flex flex-1 gap-4 overflow-hidden">
          <!-- 左侧：原始数据 -->
          <div class="flex flex-1 flex-col border border-gray-200 rounded">
            <div class="border-b border-gray-200 bg-blue-50 px-3 py-2 text-blue-800 font-medium">
              原始数据 ({{ rawDataInfo.totalRows }} 行)
            </div>
            <div class="flex-1 overflow-auto">
              <a-table
                :columns="rawDataColumns.slice(0, 5)"
                :data-source="rawData.slice(0, 20)"
                :pagination="false"
                size="small"
                :scroll="{ x: 'max-content', y: 600 }"
              />
            </div>
          </div>

          <!-- 右侧：转换后数据 -->
          <div class="flex flex-1 flex-col border border-gray-200 rounded">
            <div class="border-b border-gray-200 bg-green-50 px-3 py-2 text-green-800 font-medium">
              转换后数据
            </div>
            <div class="flex-1 overflow-auto">
              <div v-if="isRenderDataTableFormat" class="h-full">
                <a-table
                  :columns="renderDataTableColumns.slice(0, 5)"
                  :data-source="renderDataTableData.slice(0, 20)"
                  :pagination="false"
                  size="small"
                  :scroll="{ x: 'max-content', y: 600 }"
                />
              </div>
              <div v-else class="h-full">
                <pre class="h-full overflow-auto whitespace-pre-wrap bg-gray-50 p-4 text-sm">{{ formatRenderDataJson }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </c-modal>
  </div>
</template>

<script lang="ts" setup>
// #region 导入依赖
import type { ChartDataset } from '@/.generated/models/ChartDataset'
import type { ChartTemplate } from '@/.generated/models/ChartTemplate'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import { ChartStatistices } from '@/.generated/apis'
import { AggregationType } from '@/.generated/models/AggregationType'
import { ChartChartManagement } from '@/.generated/models/ChartChartManagement'
import { ChartConfig } from '@/.generated/models/ChartConfig'

import { ChartFieldType } from '@/.generated/models/ChartFieldType'
import { ConfigType } from '@/.generated/models/ConfigType'
import { FieldDataType } from '@/.generated/models/FieldDataType'
import { FilterCondition } from '@/.generated/models/FilterCondition'
import { FilterOperator } from '@/.generated/models/FilterOperator'
import { Metric } from '@/.generated/models/Metric'
import { PivotConfig } from '@/.generated/models/PivotConfig'
import ChartRenderer from '@/components/ChartRenderer.vue'
import { Guid } from '@/utils/GUID'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import { computed, h, nextTick, onMounted, onUnmounted, ref } from 'vue'
// #endregion

// #region 页面配置
definePage({
  meta: {
    layout: 'admin',
    title: '图表实例',
    local: true,
    icon: 'BarChartOutlined',
    order: 2,
  },
})
// #endregion

// #region 模板引用和表格配置
const tableRef = useTemplateRef('tableRef')

const columns = ref<ColumnProps[]>([
  {
    title: '图表标题',
    dataIndex: 'title',
    key: 'title',
    search: {
      el: 'input',
      method: 'GET',
      attrs: { placeholder: '请输入图表标题' },
    },
  },
  { title: '图表模板', key: 'chartType', width: 120 },
  { title: '数据集', key: 'dataset', width: 120 },
  { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt', dateFormat: true, width: 180 },
  { title: '操作', key: 'action', width: 150 },
])

// 获取图表列表
async function getChartList(params: any) {
  const res = await ChartStatistices.GetChartsAsync(params)
  return {
    items: res.items,
    totals: res.totals,
    offset: params.offset,
    limit: params.limit,
  }
}
// #endregion

// #region 响应式数据状态
const editVisible = ref(false)
const activeTab = ref('fieldMapping')
const editForm = ref<Partial<ChartChartManagement>>({})
const previewLoading = ref(false)
const saveLoading = ref(false)
const autoRefresh = ref(true)
const previewStatus = ref('等待配置...')
const currentChartConfig = ref<string | null>(null)
let configChangeTimer: Timeout | null = null

// 面板高度管理
const panelHeights = ref({
  fieldMapping: 300,
  styleConfig: 200,
  pivotConfig: 400,
})

// 卡片折叠状态管理
const cardCollapsed = ref({
  fieldMapping: false,
  styleConfig: false,
  pivotConfig: false,
})

// 拖拽调整高度相关
const resizing = ref({
  isResizing: false,
  panel: '',
  startY: 0,
  startHeight: 0,
})

// 图表模板和数据集选项
const chartTemplates = ref<ChartTemplate[]>([])
const datasets = ref<ChartDataset[]>([])

// 当前选中的模板和数据集
const selectedTemplate = computed(() =>
  chartTemplates.value.find(t => t.id === editForm.value.chartTypeId),
)
const selectedDataset = computed(() =>
  datasets.value.find(d => d.id === editForm.value.chartDatasetId),
)

// 字段映射配置
const fieldConfigs = ref<ChartConfig[]>([])

// 原始数据查看相关状态
const rawDataVisible = ref(false)
const rawDataLoading = ref(false)
const rawData = ref<any[]>([])
const rawDataColumns = ref<any[]>([])
const rawDataInfo = ref({
  totalRows: 0,
  totalColumns: 0,
})
const rawDataPagination = ref({
  current: 1,
  pageSize: 50,
})

// 转换后数据查看相关状态
const renderDataVisible = ref(false)
const renderDataLoading = ref(false)
const currentRenderData = ref<any>(null)
const renderDataDisplayMode = ref<'json' | 'table'>('json')

// 数据对比查看相关状态
const dataCompareVisible = ref(false)
const compareActiveTab = ref<'raw' | 'render'>('raw')
// #endregion

// #region 字段映射工具函数
// 获取字段映射的显示名称
function getFieldMappingDisplayName(fieldName: string | null | undefined): string {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return fieldName || ''

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.displayName || fieldName
}

// 获取字段映射的数据类型
function getFieldMappingDataType(fieldName: string | null | undefined): number {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return 0

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.dataType || 0
}

// 获取字段映射数据类型的文本描述
function getFieldMappingDataTypeText(fieldName: string | null | undefined): string {
  const dataType = getFieldMappingDataType(fieldName)
  const typeMap: Record<number, string> = {
    [FieldDataType.String]: '字符串',
    [FieldDataType.Number]: '数字',
    [FieldDataType.Boolean]: '布尔值',
    [FieldDataType.StringArray]: '字符串数组',
    [FieldDataType.NumberArray]: '数字数组',
    [FieldDataType.ObjectArray]: '对象数组',
  }
  return typeMap[dataType] || '未知'
}

// 判断是否为复杂字段（对象数组）
function isComplexField(fieldName: string | null | undefined): boolean {
  return getFieldMappingDataType(fieldName) === FieldDataType.ObjectArray
}

// 获取子字段映射配置
function getChildMappings(fieldName: string | null | undefined) {
  if (!selectedTemplate.value?.fieldMappings || !fieldName)
    return []

  const mapping = selectedTemplate.value.fieldMappings.find(fm => fm.name === fieldName)
  return mapping?.child || []
}

// 获取可用的数据集字段（根据字段类型过滤）
function getAvailableFields(_fieldName: string | null | undefined) {
  if (!selectedDataset.value?.fieldsJson)
    return []

  // 这里可以根据字段类型进行过滤，暂时返回所有字段
  return selectedDataset.value.fieldsJson
}
// #endregion

// #region 字段配置管理
// 获取子字段的值
function getChildFieldValue(config: ChartConfig, childFieldName: string | null | undefined): string | null {
  if (!config.children || !Array.isArray(config.children) || !childFieldName)
    return null

  const childConfig = config.children.find((item: ChartConfig) => item.fieldMapName === childFieldName)
  return childConfig?.dataFieldName || null
}

// 设置子字段的值
function setChildFieldValue(config: ChartConfig, childFieldName: string | null | undefined, value: string | null) {
  if (!childFieldName)
    return

  // 确保 config.children 是数组
  if (!Array.isArray(config.children)) {
    config.children = []
  }

  // 查找现有的子字段配置
  const existingIndex = config.children.findIndex((item: ChartConfig) => item.fieldMapName === childFieldName)

  if (existingIndex >= 0 && config.children[existingIndex]) {
    // 更新现有配置
    config.children[existingIndex]!.dataFieldName = value
  }
  else {
    // 添加新的子字段配置
    const childConfig = new ChartConfig()
    childConfig.fieldMapName = childFieldName
    childConfig.dataFieldName = value
    childConfig.defaultValue = null
    config.children.push(childConfig)
  }
}

// 样式配置
const styleConfigs = ref<ChartConfig[]>([])

// 数据透视配置
const pivotConfig = ref<PivotConfig>(new PivotConfig())

// 计算属性：可用字段
const availableFields = computed(() => {
  return selectedDataset.value?.fieldsJson || []
})

// 计算属性：数值类型字段（用于指标配置）
const numericFields = computed(() => {
  return availableFields.value.filter(field => field.type === ChartFieldType.Number)
})
// #endregion

// #region 数据加载和模板处理
// 加载图表模板和数据集选项
async function loadOptions() {
  try {
    const [templatesRes, datasetsRes] = await Promise.all([
      ChartStatistices.GetTemplatesAsync({ limit: 1000 }),
      ChartStatistices.GetDatasetsAsync({ limit: 1000 }),
    ])
    chartTemplates.value = templatesRes.items || []
    datasets.value = datasetsRes.items || []
  }
  catch (e: any) {
    message.error(`加载选项失败: ${e.message || e}`)
  }
}

// 图表模板变化处理
function onChartTypeChange() {
  if (selectedTemplate.value?.fieldMappings) {
    // 初始化字段映射配置
    fieldConfigs.value = selectedTemplate.value.fieldMappings
      .filter(fm => fm.configType === ConfigType.FieldMapping)
      .map((fm) => {
        const config = new ChartConfig()
        config.fieldMapName = fm.name
        config.dataFieldName = null

        // 如果是复杂字段（对象数组），初始化子字段配置
        if (fm.dataType === FieldDataType.ObjectArray && fm.child && fm.child.length > 0) {
          config.children = fm.child.map((child) => {
            const childConfig = new ChartConfig()
            childConfig.fieldMapName = child.name
            childConfig.dataFieldName = null
            childConfig.defaultValue = null
            return childConfig
          })
        }

        return config
      })

    // 初始化样式配置
    styleConfigs.value = selectedTemplate.value.fieldMappings
      .filter(fm => fm.configType === ConfigType.StyleConfig)
      .map((fm) => {
        const config = new ChartConfig()
        config.fieldMapName = fm.name
        config.dataFieldName = null
        return config
      })
  }
}

// 配置变化处理 - 实现所见即所得
function onConfigChange() {
  if (autoRefresh.value) {
    // 防抖处理，避免频繁刷新
    if (configChangeTimer) {
      clearTimeout(configChangeTimer)
    }
    configChangeTimer = setTimeout(() => {
      refreshPreview()
    }, 500)
  }
}

// 开始拖拽调整面板高度
function startResize(panel: string, event: MouseEvent) {
  event.preventDefault()
  resizing.value = {
    isResizing: true,
    panel,
    startY: event.clientY,
    startHeight: panelHeights.value[panel as keyof typeof panelHeights.value],
  }

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  document.body.style.cursor = 'ns-resize'
  document.body.style.userSelect = 'none'
}

// 处理拖拽调整
function handleResize(event: MouseEvent) {
  if (!resizing.value.isResizing)
    return

  const deltaY = event.clientY - resizing.value.startY
  const newHeight = Math.max(120, resizing.value.startHeight + deltaY) // 最小高度120px

  panelHeights.value[resizing.value.panel as keyof typeof panelHeights.value] = newHeight
}

// 停止拖拽调整
function stopResize() {
  resizing.value.isResizing = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 切换卡片折叠状态
function toggleCardCollapse(panel: string) {
  cardCollapsed.value[panel as keyof typeof cardCollapsed.value] = !cardCollapsed.value[panel as keyof typeof cardCollapsed.value]
}

// 自动刷新开关变化处理
function onAutoRefreshChange(checked: boolean) {
  if (checked) {
    previewStatus.value = '自动刷新已开启'
    // 立即刷新一次
    nextTick(() => {
      refreshPreview()
    })
  }
  else {
    previewStatus.value = '自动刷新已关闭'
    if (configChangeTimer) {
      clearTimeout(configChangeTimer)
      configChangeTimer = null
    }
  }
}

// 数据集变化处理
function onDatasetChange() {
  // 当数据集变化时，需要验证和重置字段映射配置
  if (!selectedDataset.value?.fieldsJson) {
    return
  }

  const availableFieldNames = selectedDataset.value.fieldsJson.map(field => field.name)
  let hasInvalidFields = false

  // 检查并重置不存在的字段映射
  fieldConfigs.value.forEach((config) => {
    if (isComplexField(config.fieldMapName)) {
      // 处理复杂字段（对象数组）
      if (Array.isArray(config.children)) {
        config.children.forEach((childConfig: ChartConfig) => {
          if (childConfig.dataFieldName && !availableFieldNames.includes(childConfig.dataFieldName)) {
            childConfig.dataFieldName = null // 重置不存在的字段
            hasInvalidFields = true
          }
        })
      }
    }
    else {
      // 处理简单字段
      if (config.dataFieldName && !availableFieldNames.includes(config.dataFieldName)) {
        config.dataFieldName = null // 重置不存在的字段
        hasInvalidFields = true
      }
    }
  })

  // 重置数据透视配置中不存在的字段
  resetInvalidPivotFields(availableFieldNames)

  // 显示提示信息
  if (fieldConfigs.value.length > 0) {
    if (hasInvalidFields) {
      message.warning('数据集已更换，部分字段映射已重置，请重新配置')
    }
    else {
      message.success('数据集已更换，字段映射配置仍然有效')
    }
  }
}

// 重置数据透视配置中无效的字段
function resetInvalidPivotFields(availableFieldNames: (string | null | undefined)[]) {
  let hasInvalidPivotFields = false

  // 重置行维度中不存在的字段
  if (pivotConfig.value.rowDimensions) {
    const validRowDimensions = pivotConfig.value.rowDimensions.filter(field =>
      field && availableFieldNames.includes(field),
    )
    if (validRowDimensions.length !== pivotConfig.value.rowDimensions.length) {
      pivotConfig.value.rowDimensions = validRowDimensions
      hasInvalidPivotFields = true
    }
  }

  // 重置列维度中不存在的字段
  if (pivotConfig.value.columnDimensions) {
    const validColumnDimensions = pivotConfig.value.columnDimensions.filter(field =>
      field && availableFieldNames.includes(field),
    )
    if (validColumnDimensions.length !== pivotConfig.value.columnDimensions.length) {
      pivotConfig.value.columnDimensions = validColumnDimensions
      hasInvalidPivotFields = true
    }
  }

  // 重置指标中不存在的字段
  if (pivotConfig.value.metrics) {
    const validMetrics = pivotConfig.value.metrics.filter(metric =>
      metric.valueField && availableFieldNames.includes(metric.valueField),
    )
    if (validMetrics.length !== pivotConfig.value.metrics.length) {
      pivotConfig.value.metrics = validMetrics
      hasInvalidPivotFields = true
    }
  }

  // 重置筛选条件中不存在的字段
  if (pivotConfig.value.filters) {
    const validFilters = pivotConfig.value.filters.filter(filter =>
      filter.field && availableFieldNames.includes(filter.field),
    )
    if (validFilters.length !== pivotConfig.value.filters.length) {
      pivotConfig.value.filters = validFilters
      hasInvalidPivotFields = true
    }
  }

  if (hasInvalidPivotFields) {
    message.warning('数据透视配置中的部分字段已重置')
  }
}

// #region 智能字段匹配算法
// 智能字段匹配建议 - 优化版本，结合数据透视配置
function suggestFieldMapping() {
  if (!selectedDataset.value?.fieldsJson || !selectedTemplate.value?.fieldMappings) {
    message.warning('请先选择图表模板和数据集')
    return
  }

  const availableFields = selectedDataset.value.fieldsJson
  let matchedCount = 0

  // 构建数据透视配置的字段使用情况
  const pivotFieldUsage = buildPivotFieldUsage()

  fieldConfigs.value.forEach((config) => {
    if (config.fieldMapName) {
      // 获取字段映射配置
      const fieldMapping = selectedTemplate.value!.fieldMappings!.find(fm => fm.name === config.fieldMapName)
      if (!fieldMapping)
        return

      if (isComplexField(config.fieldMapName)) {
        // 处理复杂字段的智能匹配
        if (Array.isArray(config.children)) {
          config.children.forEach((childConfig: ChartConfig) => {
            if (!childConfig.dataFieldName && childConfig.fieldMapName) {
              const childMapping = fieldMapping.child?.find(cm => cm.name === childConfig.fieldMapName)
              if (childMapping) {
                const suggestedField = findBestMatchWithPivot(
                  availableFields,
                  childMapping.displayName || childMapping.name || '',
                  childMapping.dataType || FieldDataType.String,
                  pivotFieldUsage,
                )
                if (suggestedField) {
                  childConfig.dataFieldName = suggestedField.name
                  matchedCount++
                }
              }
            }
          })
        }
      }
      else {
        // 处理简单字段的智能匹配
        if (!config.dataFieldName) {
          const suggestedField = findBestMatchWithPivot(
            availableFields,
            fieldMapping.displayName || fieldMapping.name || '',
            fieldMapping.dataType || FieldDataType.String,
            pivotFieldUsage,
          )
          if (suggestedField) {
            config.dataFieldName = suggestedField.name
            matchedCount++
          }
        }
      }
    }
  })

  if (matchedCount > 0) {
    message.success(`智能匹配完成，已匹配 ${matchedCount} 个字段`)
    // 匹配完成后触发配置变化
    onConfigChange()
  }
  else {
    message.info('未找到合适的字段匹配，建议先配置数据透视以提高匹配准确性')
  }
}

// 构建数据透视配置的字段使用情况
function buildPivotFieldUsage() {
  const usage = {
    rowDimensions: new Set<string>(),
    columnDimensions: new Set<string>(),
    metrics: new Set<string>(),
    filters: new Set<string>(),
  }

  // 收集行维度字段
  if (pivotConfig.value.rowDimensions) {
    pivotConfig.value.rowDimensions.forEach((field) => {
      if (field)
        usage.rowDimensions.add(field)
    })
  }

  // 收集列维度字段
  if (pivotConfig.value.columnDimensions) {
    pivotConfig.value.columnDimensions.forEach((field) => {
      if (field)
        usage.columnDimensions.add(field)
    })
  }

  // 收集指标字段
  if (pivotConfig.value.metrics) {
    pivotConfig.value.metrics.forEach((metric) => {
      if (metric.valueField)
        usage.metrics.add(metric.valueField)
    })
  }

  // 收集筛选字段
  if (pivotConfig.value.filters) {
    pivotConfig.value.filters.forEach((filter) => {
      if (filter.field)
        usage.filters.add(filter.field)
    })
  }

  return usage
}

// 增强的字段匹配算法，结合数据透视配置
function findBestMatchWithPivot(
  availableFields: any[],
  targetName: string,
  expectedDataType: FieldDataType,
  pivotFieldUsage: ReturnType<typeof buildPivotFieldUsage>,
) {
  const target = targetName.toLowerCase()

  // 根据目标字段名称推断其在图表中的作用
  const fieldRole = inferFieldRole(target)

  // 获取候选字段列表，按优先级排序
  const candidates = getCandidateFields(availableFields, target, expectedDataType, fieldRole, pivotFieldUsage)

  // 返回最佳候选字段
  return candidates.length > 0 ? candidates[0] : null
}

// 推断字段在图表中的作用
function inferFieldRole(targetName: string): 'dimension' | 'metric' | 'time' | 'category' | 'unknown' {
  const target = targetName.toLowerCase()

  // 时间相关字段
  if (['时间', '日期', '年', '月', '日', 'time', 'date', 'year', 'month', 'day', 'datetime'].some(keyword =>
    target.includes(keyword))) {
    return 'time'
  }

  // 数值指标字段
  if (['值', '数量', '金额', '总计', '平均', '最大', '最小', 'value', 'amount', 'count', 'sum', 'avg', 'max', 'min', 'total'].some(keyword =>
    target.includes(keyword))) {
    return 'metric'
  }

  // 分类维度字段
  if (['类别', '分类', '名称', '地区', '部门', 'category', 'type', 'name', 'region', 'department', 'group'].some(keyword =>
    target.includes(keyword))) {
    return 'category'
  }

  // X轴、Y轴等维度字段
  if (['x', 'y', '维度', 'dimension', 'axis'].some(keyword =>
    target.includes(keyword))) {
    return 'dimension'
  }

  return 'unknown'
}
// #endregion

// 获取候选字段列表，按优先级排序
function getCandidateFields(
  availableFields: any[],
  target: string,
  expectedDataType: FieldDataType,
  fieldRole: ReturnType<typeof inferFieldRole>,
  pivotFieldUsage: ReturnType<typeof buildPivotFieldUsage>,
) {
  const candidates: Array<{ field: any, score: number, reason: string }> = []

  availableFields.forEach((field) => {
    if (!field.name)
      return

    const fieldName = field.name.toLowerCase()
    const fieldDesc = field.description?.toLowerCase() || ''
    let score = 0
    const reasons: string[] = []

    // 1. 数据透视配置优先级匹配（最高优先级）
    if (fieldRole === 'metric' && pivotFieldUsage.metrics.has(field.name)) {
      score += 100
      reasons.push('数据透视指标字段')
    }
    else if (fieldRole === 'dimension' && pivotFieldUsage.rowDimensions.has(field.name)) {
      score += 90
      reasons.push('数据透视行维度字段')
    }
    else if (fieldRole === 'dimension' && pivotFieldUsage.columnDimensions.has(field.name)) {
      score += 85
      reasons.push('数据透视列维度字段')
    }
    else if (fieldRole === 'time' && (pivotFieldUsage.rowDimensions.has(field.name) || pivotFieldUsage.columnDimensions.has(field.name))) {
      score += 80
      reasons.push('数据透视时间维度字段')
    }

    // 2. 数据类型匹配
    if (isDataTypeCompatible(field.type, expectedDataType)) {
      score += 50
      reasons.push('数据类型匹配')
    }
    else {
      score -= 20 // 数据类型不匹配扣分
    }

    // 3. 字段名称完全匹配
    if (fieldName === target || fieldDesc === target) {
      score += 40
      reasons.push('名称完全匹配')
    }

    // 4. 字段名称包含匹配
    else if (fieldName.includes(target) || fieldDesc.includes(target)) {
      score += 30
      reasons.push('名称包含匹配')
    }

    // 5. 字段角色语义匹配
    const fieldRoleScore = getFieldRoleScore(field, fieldRole)
    score += fieldRoleScore.score
    if (fieldRoleScore.reason)
      reasons.push(fieldRoleScore.reason)

    // 6. 关键词匹配
    const keywordScore = getKeywordMatchScore(fieldName, fieldDesc, target, fieldRole)
    score += keywordScore.score
    if (keywordScore.reason)
      reasons.push(keywordScore.reason)

    // 只有得分大于0的字段才被认为是候选字段
    if (score > 0) {
      candidates.push({
        field,
        score,
        reason: reasons.join(', '),
      })
    }
  })

  // 按得分降序排序
  return candidates
    .sort((a, b) => b.score - a.score)
    .map(candidate => candidate.field)
}

// #region 智能匹配评分算法
// 检查数据类型兼容性
function isDataTypeCompatible(fieldType: ChartFieldType, expectedDataType: FieldDataType): boolean {
  // 数值类型匹配
  if (expectedDataType === FieldDataType.Number || expectedDataType === FieldDataType.NumberArray) {
    return fieldType === ChartFieldType.Number
  }

  // 字符串类型匹配
  if (expectedDataType === FieldDataType.String || expectedDataType === FieldDataType.StringArray) {
    return fieldType === ChartFieldType.String
  }

  // 布尔类型匹配
  if (expectedDataType === FieldDataType.Boolean) {
    return fieldType === ChartFieldType.Boolean
  }

  // 对象数组类型比较宽松，可以匹配多种类型
  if (expectedDataType === FieldDataType.ObjectArray) {
    return true
  }

  return false
}

// 获取字段角色匹配得分
function getFieldRoleScore(field: any, fieldRole: ReturnType<typeof inferFieldRole>): { score: number, reason?: string } {
  switch (fieldRole) {
    case 'metric':
      // 数值类型字段更适合作为指标
      if (field.type === ChartFieldType.Number) {
        return { score: 25, reason: '数值类型适合指标' }
      }
      break

    case 'time':
      // 日期类型字段更适合作为时间轴
      if (field.type === ChartFieldType.Date) {
        return { score: 25, reason: '日期类型适合时间轴' }
      }
      break

    case 'category':
    case 'dimension':
      // 字符串类型字段更适合作为分类维度
      if (field.type === ChartFieldType.String) {
        return { score: 20, reason: '字符串类型适合分类维度' }
      }
      break
  }

  return { score: 0 }
}

// 获取关键词匹配得分
function getKeywordMatchScore(
  fieldName: string,
  fieldDesc: string,
  target: string,
  fieldRole: ReturnType<typeof inferFieldRole>,
): { score: number, reason?: string } {
  let score = 0
  const reasons: string[] = []

  // 时间相关关键词
  const timeKeywords = ['时间', '日期', '年', '月', '日', 'time', 'date', 'year', 'month', 'day', 'datetime']
  if (fieldRole === 'time' || target.includes('时间') || target.includes('date')) {
    const matchedKeywords = timeKeywords.filter(keyword =>
      fieldName.includes(keyword) || fieldDesc.includes(keyword),
    )
    if (matchedKeywords.length > 0) {
      score += 15
      reasons.push('时间关键词匹配')
    }
  }

  // 数值指标关键词
  const metricKeywords = ['值', '数量', '金额', '总计', '平均', 'value', 'amount', 'count', 'sum', 'avg', 'total']
  if (fieldRole === 'metric' || target.includes('值') || target.includes('value')) {
    const matchedKeywords = metricKeywords.filter(keyword =>
      fieldName.includes(keyword) || fieldDesc.includes(keyword),
    )
    if (matchedKeywords.length > 0) {
      score += 15
      reasons.push('指标关键词匹配')
    }
  }

  // 分类维度关键词
  const categoryKeywords = ['类别', '分类', '名称', '地区', '部门', 'category', 'type', 'name', 'region', 'department']
  if (fieldRole === 'category' || fieldRole === 'dimension') {
    const matchedKeywords = categoryKeywords.filter(keyword =>
      fieldName.includes(keyword) || fieldDesc.includes(keyword),
    )
    if (matchedKeywords.length > 0) {
      score += 10
      reasons.push('分类关键词匹配')
    }
  }

  return {
    score,
    reason: reasons.length > 0 ? reasons.join(', ') : undefined,
  }
}
// #endregion

// #region 数据透视配置管理
// 数据透视配置相关方法
function addMetric() {
  if (!pivotConfig.value.metrics) {
    pivotConfig.value.metrics = []
  }
  pivotConfig.value.metrics.push(new Metric())
}

function removeMetric(index: number) {
  if (pivotConfig.value.metrics) {
    pivotConfig.value.metrics.splice(index, 1)
  }
}

function addFilter() {
  if (!pivotConfig.value.filters) {
    pivotConfig.value.filters = []
  }
  const filter = new FilterCondition()
  filter.operator = FilterOperator.Equal // 默认使用"等于"操作符
  pivotConfig.value.filters.push(filter)
}

function removeFilter(index: number) {
  if (pivotConfig.value.filters) {
    pivotConfig.value.filters.splice(index, 1)
  }
}

// 智能推荐数据透视配置
function suggestPivotConfig() {
  if (!selectedDataset.value?.fieldsJson) {
    message.warning('请先选择数据集')
    return
  }

  const fields = selectedDataset.value.fieldsJson
  let suggestedCount = 0

  // 智能推荐行维度：优先选择字符串类型的字段
  const stringFields = fields.filter(field => field.type === ChartFieldType.String)
  if (stringFields.length > 0 && (!pivotConfig.value.rowDimensions || pivotConfig.value.rowDimensions.length === 0)) {
    const firstStringField = stringFields[0]
    if (firstStringField && firstStringField.name) {
      pivotConfig.value.rowDimensions = [firstStringField.name]
      suggestedCount++
    }
  }

  // 智能推荐列维度：选择第二个字符串字段或日期字段
  const dateFields = fields.filter(field => field.type === ChartFieldType.Date)
  const candidateColumnFields = [...dateFields, ...stringFields.slice(1)]
  if (candidateColumnFields.length > 0 && (!pivotConfig.value.columnDimensions || pivotConfig.value.columnDimensions.length === 0)) {
    const firstCandidateField = candidateColumnFields[0]
    if (firstCandidateField && firstCandidateField.name) {
      pivotConfig.value.columnDimensions = [firstCandidateField.name]
      suggestedCount++
    }
  }

  // 智能推荐指标：选择数值类型字段
  if (numericFields.value.length > 0 && (!pivotConfig.value.metrics || pivotConfig.value.metrics.length === 0)) {
    const firstNumericField = numericFields.value[0]
    if (firstNumericField && firstNumericField.name) {
      const metric = new Metric()
      metric.valueField = firstNumericField.name
      metric.aggregation = AggregationType.Sum // 默认使用求和
      pivotConfig.value.metrics = [metric]
      suggestedCount++
    }
  }

  if (suggestedCount > 0) {
    message.success(`智能推荐完成，已配置 ${suggestedCount} 项设置`)

    // 数据透视配置完成后，自动触发字段映射的智能匹配
    if (selectedTemplate.value?.fieldMappings && fieldConfigs.value.length > 0) {
      nextTick(() => {
        suggestFieldMapping()
      })
    }
  }
  else {
    message.info('当前配置已完整，无需推荐')
  }
}

// 清空数据透视配置
function clearPivotConfig() {
  pivotConfig.value = new PivotConfig()
  message.success('数据透视配置已清空')
}
// #endregion

// #region 图表编辑和保存管理
function openEditModal(record?: ChartChartManagement) {
  if (record) {
    editForm.value = { ...record }
    // 加载现有的字段映射和样式配置
    fieldConfigs.value = record.fieldConfigJson || []
    styleConfigs.value = record.styleConfigJson || []
    // 加载现有的数据透视配置
    pivotConfig.value = record.pivotConfig ? { ...record.pivotConfig } : new PivotConfig()

    // 如果已有模板选择，触发模板变化处理以确保配置结构正确
    if (record.chartTypeId) {
      nextTick(() => {
        onChartTypeChange()
        // 重新应用已保存的配置值
        if (record.fieldConfigJson) {
          mergeExistingFieldConfigs(record.fieldConfigJson)
        }
      })
    }
  }
  else {
    editForm.value = new ChartChartManagement()
    fieldConfigs.value = []
    styleConfigs.value = []
    pivotConfig.value = new PivotConfig()
  }
  editVisible.value = true
  activeTab.value = 'fieldMapping'
}

// 合并现有的字段配置到新的配置结构中
function mergeExistingFieldConfigs(existingConfigs: ChartConfig[]) {
  existingConfigs.forEach((existingConfig) => {
    const currentConfig = fieldConfigs.value.find(fc => fc.fieldMapName === existingConfig.fieldMapName)
    if (currentConfig) {
      currentConfig.dataFieldName = existingConfig.dataFieldName
      currentConfig.children = existingConfig.children
      currentConfig.defaultValue = existingConfig.defaultValue
    }
  })
}

async function handleEditOk() {
  saveLoading.value = true
  try {
    if (!editForm.value.title) {
      message.warning('请填写图表标题')
      return
    }
    if (!editForm.value.chartTypeId) {
      message.warning('请选择图表模板')
      return
    }
    if (!editForm.value.chartDatasetId) {
      message.warning('请选择数据集')
      return
    }

    // 设置字段映射、样式配置和数据透视配置
    editForm.value.fieldConfigJson = fieldConfigs.value
    editForm.value.styleConfigJson = styleConfigs.value
    editForm.value.pivotConfig = pivotConfig.value

    if (Guid.isNotNull(editForm.value.id)) {
      await ChartStatistices.UpdateChart_PostAsync(
        { id: String(editForm.value.id) },
        editForm.value as ChartChartManagement,
      )
      message.success('更新成功')
    }
    else {
      await ChartStatistices.CreateChart_PostAsync(editForm.value as ChartChartManagement)
      message.success('创建成功')
    }
    editVisible.value = false
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`操作失败: ${e.message || e}`)
  }
  finally {
    saveLoading.value = false
  }
}

async function deleteChart(record: ChartChartManagement) {
  try {
    await ChartStatistices.DeleteChart_PostAsync({ id: String(record.id) })
    message.success('删除成功')
    tableRef.value?.search()
  }
  catch (e: any) {
    message.error(`删除失败: ${e.message || e}`)
  }
}

// 转换后数据相关计算属性
const formatRenderDataJson = computed(() => {
  if (!currentRenderData.value)
    return '暂无数据'
  try {
    return JSON.stringify(currentRenderData.value, null, 2)
  }
  catch (e) {
    return String(currentRenderData.value)
  }
})

// 判断转换后数据是否支持表格格式显示
const isRenderDataTableFormat = computed(() => {
  if (!currentRenderData.value)
    return false

  // 检查是否为数组格式
  if (Array.isArray(currentRenderData.value)) {
    return currentRenderData.value.length > 0 && typeof currentRenderData.value[0] === 'object'
  }

  // 检查是否为对象且包含数组数据
  if (typeof currentRenderData.value === 'object') {
    const keys = Object.keys(currentRenderData.value)
    return keys.some(key => Array.isArray(currentRenderData.value[key]))
  }

  return false
})

// 转换后数据的表格列配置
const renderDataTableColumns = computed(() => {
  if (!isRenderDataTableFormat.value)
    return []

  const data = currentRenderData.value

  if (Array.isArray(data) && data.length > 0) {
    // 数组格式：使用第一个对象的键作为列
    const firstItem = data[0]
    return Object.keys(firstItem).map(key => ({
      title: key,
      dataIndex: key,
      key,
      width: 150,
      ellipsis: true,
      customRender: ({ text }: any) => {
        if (text === null || text === undefined) {
          return { children: '-', class: 'text-gray-400' }
        }
        if (typeof text === 'object') {
          return { children: JSON.stringify(text), class: 'text-blue-600' }
        }
        return text
      },
    }))
  }

  if (typeof data === 'object') {
    // 对象格式：查找第一个数组字段
    const arrayKey = Object.keys(data).find(key => Array.isArray(data[key]))
    if (arrayKey && data[arrayKey].length > 0) {
      const firstItem = data[arrayKey][0]
      if (typeof firstItem === 'object') {
        return Object.keys(firstItem).map(key => ({
          title: key,
          dataIndex: key,
          key,
          width: 150,
          ellipsis: true,
        }))
      }
    }
  }

  return []
})

// 转换后数据的表格数据
const renderDataTableData = computed(() => {
  if (!isRenderDataTableFormat.value)
    return []

  const data = currentRenderData.value

  if (Array.isArray(data)) {
    return data
  }

  if (typeof data === 'object') {
    // 查找第一个数组字段
    const arrayKey = Object.keys(data).find(key => Array.isArray(data[key]))
    if (arrayKey) {
      return data[arrayKey]
    }
  }

  return []
})
// #endregion

// #region 图表预览管理
async function previewChart(record: ChartChartManagement) {
  try {
    const chartConfig = await ChartStatistices.PreviewChart_GetAsync({ id: String(record.id) })

    // 创建预览弹窗
    Modal.info({
      title: `图表预览 - ${record.title}`,
      content: h(ChartRenderer, {
        config: chartConfig,
        class: 'h-50vh!',
      }),
      width: 900,
      onOk() {

      },
    })
  }
  catch (e: any) {
    message.error(`预览失败: ${e.message || e}`)
  }
}

// 刷新预览
async function refreshPreview() {
  if (!selectedTemplate.value || !selectedDataset.value) {
    previewStatus.value = '请先选择图表模板和数据集'
    return
  }

  // 检查是否有必要的字段映射
  const hasValidMapping = fieldConfigs.value.some((config) => {
    if (isComplexField(config.fieldMapName)) {
      return config.children && config.children.some(child => child.dataFieldName)
    }
    return config.dataFieldName
  })

  if (!hasValidMapping) {
    previewStatus.value = '请配置字段映射'
    return
  }

  previewLoading.value = true
  previewStatus.value = '正在生成预览...'
  try {
    // 构建预览数据
    const previewData = new ChartChartManagement()
    previewData.id = editForm.value.id
    previewData.title = editForm.value.title ?? '未命名图表'
    previewData.chartTypeId = editForm.value.chartTypeId
    previewData.chartDatasetId = editForm.value.chartDatasetId
    previewData.fieldConfigJson = fieldConfigs.value
    previewData.styleConfigJson = styleConfigs.value
    previewData.pivotConfig = pivotConfig.value
    previewData.updatedAt = editForm.value.updatedAt || dayjs()

    // 调用后端API获取预览数据
    const chartConfig = await ChartStatistices.PreviewChartByData_PostAsync(
      {
        datasetId: String(selectedDataset.value.id),
        templateId: String(selectedTemplate.value.id),
      },
      previewData,
    )

    // 更新图表配置，让 ChartRenderer 组件自动渲染
    currentChartConfig.value = chartConfig.options
    // 保存转换后的数据
    currentRenderData.value = chartConfig.renderData
    previewStatus.value = `预览更新时间: ${new Date().toLocaleTimeString()}`
  }
  catch (e: any) {
    console.error('预览失败:', e)
    previewStatus.value = '预览生成失败'

    // 如果API调用失败，使用模拟数据
    try {
      const mockData = generateMockChartData()
      currentChartConfig.value = JSON.stringify(mockData, null, 2)
      previewStatus.value = `模拟预览 - ${new Date().toLocaleTimeString()}`
    }
    catch {
      message.error(`预览失败: ${e.message || e}`)
    }
  }
  finally {
    previewLoading.value = false
  }
}

// 查看原始数据
async function viewRawData() {
  if (!selectedDataset.value) {
    message.warning('请先选择数据集')
    return
  }

  rawDataVisible.value = true
  await loadRawData()
}

// 加载原始数据
async function loadRawData() {
  if (!selectedDataset.value)
    return

  rawDataLoading.value = true
  try {
    // 获取完整的数据集信息
    const datasetDetail = await ChartStatistices.GetDatasetAsync({
      id: String(selectedDataset.value.id),
    })

    // 处理数据集的原始数据
    if (datasetDetail.records && datasetDetail.records.length > 0) {
      // 将二维数组转换为对象数组
      const allData = datasetDetail.records.map((row: any[]) => {
        const rowObj: any = {}
        if (datasetDetail.fieldsJson) {
          datasetDetail.fieldsJson.forEach((field, index) => {
            rowObj[field.name || `field_${index}`] = row[index]
          })
        }
        return rowObj
      })

      // 实现分页
      const startIndex = (rawDataPagination.value.current - 1) * rawDataPagination.value.pageSize
      const endIndex = startIndex + rawDataPagination.value.pageSize
      rawData.value = allData.slice(startIndex, endIndex)

      rawDataInfo.value = {
        totalRows: allData.length,
        totalColumns: datasetDetail.fieldsJson?.length || 0,
      }

      // 生成表格列配置
      if (datasetDetail.fieldsJson) {
        rawDataColumns.value = datasetDetail.fieldsJson.map((field: any) => ({
          title: field.displayName || field.name,
          dataIndex: field.name,
          key: field.name,
          width: 150,
          ellipsis: true,
          sorter: true,
          customRender: ({ text }: any) => {
            // 处理不同数据类型的显示
            if (text === null || text === undefined) {
              return { children: '-', class: 'text-gray-400' }
            }
            if (typeof text === 'object') {
              return { children: JSON.stringify(text), class: 'text-blue-600' }
            }
            if (typeof text === 'boolean') {
              return { children: text ? '是' : '否', class: text ? 'text-green-600' : 'text-red-600' }
            }
            if (typeof text === 'number') {
              return { children: text.toLocaleString(), class: 'text-blue-600' }
            }
            return text
          },
        }))
      }
    }
    else {
      // 如果没有数据记录，生成模拟数据用于演示
      generateMockRawData()
    }
  }
  catch (e: any) {
    console.error('加载原始数据失败:', e)
    message.error(`加载数据失败: ${e.message || e}`)

    // 如果API调用失败，生成模拟数据用于演示
    generateMockRawData()
  }
  finally {
    rawDataLoading.value = false
  }
}

// 生成模拟原始数据（用于演示）
function generateMockRawData() {
  if (!selectedDataset.value?.fieldsJson)
    return

  const fields = selectedDataset.value.fieldsJson
  const mockData: any[] = []

  // 生成50条模拟数据
  for (let i = 1; i <= 50; i++) {
    const row: any = {}
    fields.forEach((field: any) => {
      switch (field.type) {
        case 'String':
          row[field.name] = `示例数据${i}`
          break
        case 'Number':
          row[field.name] = Math.floor(Math.random() * 1000) + 1
          break
        case 'Date':
          row[field.name] = dayjs().subtract(Math.floor(Math.random() * 365), 'day').format('YYYY-MM-DD')
          break
        case 'Boolean':
          row[field.name] = Math.random() > 0.5
          break
        default:
          row[field.name] = `数据${i}`
      }
    })
    mockData.push(row)
  }

  rawData.value = mockData
  rawDataInfo.value = {
    totalRows: 50,
    totalColumns: fields.length,
  }

  rawDataColumns.value = fields.map((field: any) => ({
    title: field.displayName || field.name,
    dataIndex: field.name,
    key: field.name,
    width: 150,
    ellipsis: true,
    sorter: true,
    customRender: ({ text }: any) => {
      if (text === null || text === undefined) {
        return { children: '-', class: 'text-gray-400' }
      }
      if (typeof text === 'boolean') {
        return { children: text ? '是' : '否', class: text ? 'text-green-600' : 'text-red-600' }
      }
      return text
    },
  }))
}

// 刷新原始数据
async function refreshRawData() {
  rawDataPagination.value.current = 1
  await loadRawData()
}

// 处理表格分页变化
function handleRawDataTableChange(pagination: any) {
  rawDataPagination.value.current = pagination.current
  rawDataPagination.value.pageSize = pagination.pageSize
  loadRawData()
}

// 导出原始数据
function exportRawData() {
  if (!rawData.value.length) {
    message.warning('没有数据可导出')
    return
  }

  try {
    // 准备导出数据
    const exportData = rawData.value.map((row) => {
      const newRow: any = {}
      rawDataColumns.value.forEach((col) => {
        newRow[col.title] = row[col.dataIndex]
      })
      return newRow
    })

    // 转换为CSV格式
    const headers = rawDataColumns.value.map(col => col.title).join(',')
    const csvContent = [
      headers,
      ...exportData.map(row =>
        rawDataColumns.value.map((col) => {
          const value = row[col.title]
          // 处理包含逗号的值
          if (typeof value === 'string' && value.includes(',')) {
            return `"${value}"`
          }
          return value || ''
        }).join(','),
      ),
    ].join('\n')

    // 创建下载链接
    const blob = new Blob([`\uFEFF${csvContent}`], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${selectedDataset.value?.name || '原始数据'}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    message.success('数据导出成功')
  }
  catch (e: any) {
    console.error('导出数据失败:', e)
    message.error(`导出失败: ${e.message || e}`)
  }
}

// 查看转换后数据
function viewRenderData() {
  if (!currentRenderData.value) {
    message.warning('请先生成图表预览以获取转换后数据')
    return
  }

  renderDataVisible.value = true
}

// 查看数据对比
async function viewDataCompare() {
  if (!selectedDataset.value) {
    message.warning('请先选择数据集')
    return
  }

  if (!currentRenderData.value) {
    message.warning('请先生成图表预览以获取转换后数据')
    return
  }

  dataCompareVisible.value = true

  // 如果原始数据还没有加载，先加载原始数据
  if (!rawData.value.length) {
    await loadRawData()
  }
}

// 导出转换后数据
function exportRenderData() {
  if (!currentRenderData.value) {
    message.warning('没有转换后数据可导出')
    return
  }

  try {
    let exportContent = ''
    let fileName = ''

    if (isRenderDataTableFormat.value) {
      // 表格格式：导出为CSV
      const data = renderDataTableData.value
      const columns = renderDataTableColumns.value

      const headers = columns.map(col => col.title).join(',')
      const csvContent = [
        headers,
        ...data.map(row =>
          columns.map((col) => {
            const value = row[col.dataIndex]
            // 处理包含逗号的值
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value}"`
            }
            return value || ''
          }).join(','),
        ),
      ].join('\n')

      exportContent = `\uFEFF${csvContent}`
      fileName = `转换后数据_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    }
    else {
      // JSON格式：导出为JSON文件
      exportContent = JSON.stringify(currentRenderData.value, null, 2)
      fileName = `转换后数据_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.json`
    }

    // 创建下载链接
    const blob = new Blob([exportContent], {
      type: isRenderDataTableFormat.value ? 'text/csv;charset=utf-8;' : 'application/json;charset=utf-8;',
    })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    message.success('转换后数据导出成功')
  }
  catch (e: any) {
    console.error('导出转换后数据失败:', e)
    message.error(`导出失败: ${e.message || e}`)
  }
}
// #endregion

// #region 工具函数和生命周期管理
// 根据当前配置生成模拟图表数据
function generateMockChartData() {
  const templateName = selectedTemplate.value?.name || ''

  // 根据图表类型生成不同的模拟数据
  if (templateName.includes('柱状图') || templateName.includes('bar')) {
    return {
      title: {
        text: editForm.value.title || '图表标题',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: ['一月', '二月', '三月', '四月', '五月', '六月'],
      },
      yAxis: {
        type: 'value',
      },
      series: [{
        name: '数据系列',
        type: 'bar',
        data: [120, 200, 150, 80, 70, 110],
        itemStyle: {
          color: '#5470c6',
        },
      }],
    }
  }
  else if (templateName.includes('折线图') || templateName.includes('line')) {
    return {
      title: {
        text: editForm.value.title || '图表标题',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: ['一月', '二月', '三月', '四月', '五月', '六月'],
      },
      yAxis: {
        type: 'value',
      },
      series: [{
        name: '数据系列',
        type: 'line',
        data: [120, 200, 150, 80, 70, 110],
        smooth: true,
      }],
    }
  }
  else if (templateName.includes('饼图') || templateName.includes('pie')) {
    return {
      title: {
        text: editForm.value.title || '图表标题',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
      },
      series: [{
        name: '数据分布',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '类别A' },
          { value: 735, name: '类别B' },
          { value: 580, name: '类别C' },
          { value: 484, name: '类别D' },
          { value: 300, name: '类别E' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      }],
    }
  }
  else {
    // 默认柱状图
    return {
      title: {
        text: editForm.value.title || '图表标题',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: ['数据1', '数据2', '数据3', '数据4', '数据5'],
      },
      yAxis: {
        type: 'value',
      },
      series: [{
        name: '数据系列',
        type: 'bar',
        data: [120, 200, 150, 80, 70],
      }],
    }
  }
}

onMounted(() => {
  loadOptions()
})

onUnmounted(() => {
  // 清理定时器
  if (configChangeTimer) {
    clearTimeout(configChangeTimer)
    configChangeTimer = null
  }
})
// #endregion
</script>

<style scoped>
.chart-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-preview-content {
  flex: 1;
  min-height: 0;
}

/* 配置卡片样式 */
.config-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease;
  /* min-height: 120px; */
}

.config-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.config-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-bottom: 2px solid #e5e7eb;
  cursor: default;
  user-select: none;
  position: relative;
  border-radius: 10px 10px 0 0;
}

.config-card-header:hover {
  background: #f3f4f6;
}

.config-card-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  min-height: 0;
  border-radius: 0 0 10px 10px;
}

.resize-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: ns-resize;
  color: #9ca3af;
  border-radius: 4px;
  transition: all 0.2s;
}

.resize-handle:hover {
  background: #e5e7eb;
  color: #6b7280;
}

.collapse-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.collapse-handle:hover {
  background: #e5e7eb;
}

.config-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
}

/* 拖拽时的全局样式 */
body.resizing {
  cursor: ns-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: ns-resize !important;
  user-select: none !important;
}

/* 滚动条样式优化 */
.config-card-content::-webkit-scrollbar {
  width: 4px;
}

.config-card-content::-webkit-scrollbar-track {
  background: transparent;
}

.config-card-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.config-card-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 增强视觉分层的额外样式 */
.config-label {
  position: relative;
  padding-left: 8px;
}

.config-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

/* 表单区域分组样式 */
.form-group {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
  margin-bottom: 12px;
}

.form-group:hover {
  border-color: #d1d5db;
  background: #f9f9f9;
}

/* 预览区域增强 */
.preview-container {
  position: relative;
  overflow: hidden;
}

.preview-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  z-index: 1;
}
</style>
